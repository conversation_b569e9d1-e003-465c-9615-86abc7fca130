@use 'design-tokens' as *;

.header-layout {
  @include text-preset-4--bold;
  margin-bottom: $spacing-2;
  margin-left: $spacing-2;
}

.multi-location-analytics {
  padding: $spacing-4;

  .date-range-header {
    @include text-preset-5--bold;
    color: $primary-text-color;
    margin-bottom: $spacing-3;
    padding: 0;
    text-align: left;

    sup {
      margin-left: $spacing-1;
      color: $primary-text-color;
      font-size: $font-size-1;
      font-weight: $font-weight-bold;
    }
  }

  .card-row {
    display: flex;
    flex-direction: column;
    gap: $spacing-4;
  }
}

